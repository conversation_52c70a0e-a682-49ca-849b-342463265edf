# 📊 Enhanced Telemetry Features - Diverse HTTP Status Codes

## 🎯 Overview

The distributed services have been enhanced to generate **realistic telemetry data** with diverse HTTP status codes, including 200 OK responses, 4xx client errors, and 5xx server errors. This creates comprehensive observability data for monitoring and alerting.

## 🔄 HTTP Status Code Distribution

### ✅ Success Responses (200-299)
- **200 OK**: Health checks, successful API calls, data retrieval
- **201 Created**: Successful data processing operations

### ⚠️ Client Errors (400-499)
- **400 Bad Request**: Invalid parameters, malformed requests
- **401 Unauthorized**: Missing or expired authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **408 Request Timeout**: Request processing timeout
- **410 Gone**: Resource permanently unavailable
- **422 Unprocessable Entity**: Validation failures
- **429 Too Many Requests**: Rate limiting

### 🚨 Server Errors (500-599)
- **500 Internal Server Error**: Processing failures, exceptions
- **502 Bad Gateway**: Upstream service errors
- **503 Service Unavailable**: Service overload, maintenance
- **504 Gateway Timeout**: Upstream service timeout

## 🎲 Error Simulation Strategies

### 1. **Random Error Generation**
- Each service has configurable error rates (10-25% depending on endpoint)
- Weighted distribution of different error types
- Realistic error scenarios based on business logic

### 2. **Deterministic Error Triggers**
- Specific parameter values trigger known errors
- `simulate_error=true` query parameter forces errors
- Predictable test scenarios for validation

### 3. **Load-Based Errors**
- Higher error rates during stress testing
- Resource exhaustion simulation
- Timeout scenarios under load

## 🌐 Service-Specific Endpoints

### Node.js Service (Port 3000)
```bash
# Success scenarios
curl http://localhost:3000/health                    # 200 OK
curl http://localhost:3000/api/orders/12345         # 200 OK

# Error scenarios  
curl http://localhost:3000/api/orders/invalid       # 400 Bad Request
curl http://localhost:3000/api/orders/unauthorized  # 401 Unauthorized
curl http://localhost:3000/api/orders/notfound      # 404 Not Found

# Slow operations
curl http://localhost:3000/api/timeout              # 200 OK (slow) or 500

# POST operations
curl -X POST http://localhost:3000/api/process      # 201, 422, or 500
```

### Python Service (Port 5000)
```bash
# Success scenarios
curl http://localhost:5000/health                    # 200 OK
curl http://localhost:5000/api/products/widget123   # 200 OK

# Error scenarios
curl http://localhost:5000/api/products/invalid     # 400 Bad Request
curl http://localhost:5000/api/products/notfound    # 404 Not Found
curl http://localhost:5000/api/products/discontinued # 410 Gone

# Heavy operations
curl http://localhost:5000/api/heavy                # 200 OK or 500

# POST with JSON
curl -X POST -H "Content-Type: application/json" \
     -d '{"operation":"divide","a":10,"b":0}' \
     http://localhost:5000/api/calculate            # 400 Bad Request
```

### Java Service (Port 8080)
```bash
# Success scenarios
curl http://localhost:8080/health                   # 200 OK
curl http://localhost:8080/api/users/user456       # 200 OK

# Error scenarios
curl http://localhost:8080/api/users/invalid       # 400 Bad Request
curl http://localhost:8080/api/users/forbidden     # 403 Forbidden
curl http://localhost:8080/api/users/notfound      # 404 Not Found

# Slow operations
curl http://localhost:8080/api/slow                 # 200 OK (slow) or 500
```

### Go Service (Port 8090)
```bash
# Success scenarios
curl http://localhost:8090/health                   # 200 OK
curl http://localhost:8090/api/inventory/item789   # 200 OK

# Error scenarios
curl http://localhost:8090/api/inventory/invalid   # 400 Bad Request
curl http://localhost:8090/api/inventory/restricted # 403 Forbidden
curl http://localhost:8090/api/inventory/notfound  # 404 Not Found

# Stress testing
curl http://localhost:8090/api/stress              # 200 OK or 500

# POST validation
curl -X POST -H "Content-Type: application/json" \
     -d '{"type":"invalid"}' \
     http://localhost:8090/api/validate            # 400 Bad Request
```

## 🚀 Traffic Generation Script

The `generate-traffic.sh` script creates realistic traffic patterns:

```bash
# Run the traffic generator
sudo /opt/distributed-services/generate-traffic.sh
```

### Traffic Distribution:
- **50%** Success requests (200 OK)
- **25%** Error requests (4xx, 5xx)
- **10%** Slow requests (timeouts, heavy processing)
- **10%** POST requests with various payloads
- **5%** Explicit error simulation
- **Periodic bursts** every 50 requests

## 📈 Telemetry Benefits

### 1. **Comprehensive Monitoring**
- Full spectrum of HTTP status codes
- Realistic error rates and patterns
- Performance characteristics under load

### 2. **Alerting Validation**
- Test alert thresholds with real error scenarios
- Validate escalation procedures
- Practice incident response

### 3. **Dashboard Completeness**
- Error rate visualizations
- Status code distribution charts
- Service dependency mapping
- Performance trend analysis

### 4. **Distributed Tracing**
- Error propagation across services
- Timeout and retry patterns
- Service interaction mapping
- Performance bottleneck identification

## 🔧 Configuration Options

### Error Rate Adjustment
Modify error probabilities in service code:
```javascript
// Node.js - adjust error rate
const simulateError = Math.random() < 0.12; // 12% error rate
```

### Custom Error Scenarios
Add new error endpoints following existing patterns:
```python
# Python - add new error scenario
@app.route('/api/custom/<param>')
def custom_endpoint(param):
    if param == 'trigger_error':
        return jsonify({'error': 'Custom error'}), 418  # I'm a teapot
```

### Traffic Pattern Customization
Modify `generate-traffic.sh` for different patterns:
- Adjust request frequencies
- Add new endpoint combinations
- Change error simulation rates
- Customize burst patterns

## 📊 Expected Telemetry Data

With this enhanced setup, you'll see:

### In Coralogix:
- **Diverse span statuses** (OK, ERROR, TIMEOUT)
- **HTTP status code metrics** across all services
- **Error rate trends** and anomaly detection
- **Service dependency graphs** with error propagation
- **Performance histograms** with outliers

### In CloudWatch:
- **Custom metrics** for each status code
- **Error rate alarms** and notifications
- **Performance dashboards** with percentiles
- **Log aggregation** with error correlation

This creates a **production-like observability environment** perfect for testing monitoring tools, validating alerts, and training teams on incident response! 🎯
