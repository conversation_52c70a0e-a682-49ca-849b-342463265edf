AWSTemplateFormatVersion: '2010-09-09'
Description: 'EC2 instance with distributed services, OpenTelemetry instrumentation, and Coralogix monitoring'

Parameters:
  VpcId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID where the EC2 instance will be launched

  SubnetId:
    Type: AWS::EC2::Subnet::Id
    Description: Subnet ID where the EC2 instance will be launched

  InstanceType:
    Type: String
    Default: t3.large
    AllowedValues:
      - t3.medium
      - t3.large
      - t3.xlarge
      - t3.2xlarge
      - m5.large
      - m5.xlarge
      - m5.2xlarge
    Description: EC2 instance type

  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: EC2 Key Pair for SSH access

  CoralogixPrivateKey:
    Type: String
    NoEcho: true
    Description: Coralogix Private Key for telemetry data

  CoralogixApplicationName:
    Type: String
    Default: distributed-services-demo
    Description: Application name for Coralogix

  CoralogixSubsystemName:
    Type: String
    Default: ec2-services
    Description: Subsystem name for Coralogix

Resources:
  # IAM Role for EC2 instance
  EC2Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub '${AWS::StackName}_EC2Role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
        - arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy
      Policies:
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource: '*'
        - PolicyName: CloudWatchLogs
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogStreams
                  - logs:DescribeLogGroups
                Resource: '*'

  # Instance Profile
  EC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - !Ref EC2Role

  # Security Group
  SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub '${AWS::StackName}_SecurityGroup'
      GroupDescription: Security group for distributed services EC2 instance
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0
          Description: SSH access
        - IpProtocol: tcp
          FromPort: 3000
          ToPort: 3000
          CidrIp: 0.0.0.0/0
          Description: Node.js service
        - IpProtocol: tcp
          FromPort: 5000
          ToPort: 5000
          CidrIp: 0.0.0.0/0
          Description: Python service
        - IpProtocol: tcp
          FromPort: 8080
          ToPort: 8080
          CidrIp: 0.0.0.0/0
          Description: Java service
        - IpProtocol: tcp
          FromPort: 8090
          ToPort: 8090
          CidrIp: 0.0.0.0/0
          Description: Go service
        - IpProtocol: tcp
          FromPort: 4317
          ToPort: 4318
          CidrIp: 0.0.0.0/0
          Description: OTEL Collector OTLP
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: All outbound traffic

  # EC2 Instance
  EC2Instance:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: ami-0c02fb55956c7d316  # Amazon Linux 2023 AMI (update as needed)
      InstanceType: !Ref InstanceType
      KeyName: !Ref KeyPairName
      SubnetId: !Ref SubnetId
      SecurityGroupIds:
        - !Ref SecurityGroup
      IamInstanceProfile: !Ref EC2InstanceProfile
      Tags:
        - Key: Name
          Value: !Sub '${AWS::StackName}_DistributedServices'
        - Key: Environment
          Value: demo
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          yum update -y
          echo "Setup complete" > /var/log/setup.log

          # Create Node.js service
          mkdir -p /opt/distributed-services/nodejs-service
          cd /opt/distributed-services/nodejs-service

          cat > package.json << 'EOF'
          {
            "name": "nodejs-service",
            "version": "1.0.0",
            "description": "Node.js service with OpenTelemetry",
            "main": "app.js",
            "scripts": {
              "start": "node app.js"
            },
            "dependencies": {
              "express": "^4.18.2",
              "@opentelemetry/api": "^1.7.0",
              "@opentelemetry/sdk-node": "^0.45.0",
              "@opentelemetry/auto-instrumentations-node": "^0.40.0",
              "@opentelemetry/exporter-otlp-grpc": "^0.45.0",
              "axios": "^1.6.0"
            }
          }
          EOF

          cat > app.js << 'EOF'
          const { NodeSDK } = require('@opentelemetry/sdk-node');
          const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
          const { OTLPTraceExporter } = require('@opentelemetry/exporter-otlp-grpc');

          const sdk = new NodeSDK({
            traceExporter: new OTLPTraceExporter({
              url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4317',
            }),
            instrumentations: [getNodeAutoInstrumentations()],
          });

          sdk.start();

          const express = require('express');
          const axios = require('axios');
          const app = express();
          const port = 3000;

          app.use(express.json());

          app.get('/health', (req, res) => {
            res.json({ service: 'nodejs', status: 'healthy', timestamp: new Date().toISOString() });
          });

          app.get('/api/data', async (req, res) => {
            try {
              // Call Python service
              const pythonResponse = await axios.get('http://localhost:5000/health');
              res.json({
                service: 'nodejs',
                data: 'Hello from Node.js service',
                python_service: pythonResponse.data,
                timestamp: new Date().toISOString()
              });
            } catch (error) {
              res.status(500).json({ error: error.message });
            }
          });

          app.listen(port, () => {
            console.log('Node.js service listening at http://localhost:' + port);
          });
          EOF

          # Install Node.js dependencies
          npm install

          # Create Python service
          cd /opt/distributed-services
          mkdir -p python-service
          cd python-service

          cat > requirements.txt << 'EOF'
          flask==3.0.0
          opentelemetry-api==1.21.0
          opentelemetry-sdk==1.21.0
          opentelemetry-instrumentation-flask==0.42b0
          opentelemetry-instrumentation-requests==0.42b0
          opentelemetry-exporter-otlp-proto-grpc==1.21.0
          requests==2.31.0
          EOF

          cat > app.py << 'EOF'
          from opentelemetry import trace
          from opentelemetry.sdk.trace import TracerProvider
          from opentelemetry.sdk.trace.export import BatchSpanProcessor
          from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
          from opentelemetry.instrumentation.flask import FlaskInstrumentor
          from opentelemetry.instrumentation.requests import RequestsInstrumentor
          from flask import Flask, jsonify
          import requests
          import os
          from datetime import datetime

          # Initialize OpenTelemetry
          trace.set_tracer_provider(TracerProvider())
          tracer = trace.get_tracer(__name__)

          otlp_exporter = OTLPSpanExporter(
              endpoint=os.getenv('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317'),
              insecure=True
          )

          span_processor = BatchSpanProcessor(otlp_exporter)
          trace.get_tracer_provider().add_span_processor(span_processor)

          app = Flask(__name__)

          # Instrument Flask and requests
          FlaskInstrumentor().instrument_app(app)
          RequestsInstrumentor().instrument()

          @app.route('/health')
          def health():
              return jsonify({
                  'service': 'python',
                  'status': 'healthy',
                  'timestamp': datetime.now().isoformat()
              })

          @app.route('/api/data')
          def get_data():
              try:
                  # Call Java service
                  java_response = requests.get('http://localhost:8080/health', timeout=5)
                  return jsonify({
                      'service': 'python',
                      'data': 'Hello from Python service',
                      'java_service': java_response.json(),
                      'timestamp': datetime.now().isoformat()
                  })
              except Exception as e:
                  return jsonify({'error': str(e)}), 500

          if __name__ == '__main__':
              app.run(host='0.0.0.0', port=5000, debug=False)
          EOF

          # Install Python dependencies
          pip3 install -r requirements.txt

          # Create Java service
          cd /opt/distributed-services
          mkdir -p java-service/src/main/java/com/example
          cd java-service

          cat > pom.xml << 'EOF'
          <?xml version="1.0" encoding="UTF-8"?>
          <project xmlns="http://maven.apache.org/POM/4.0.0"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                   http://maven.apache.org/xsd/maven-4.0.0.xsd">
              <modelVersion>4.0.0</modelVersion>
              <groupId>com.example</groupId>
              <artifactId>java-service</artifactId>
              <version>1.0.0</version>
              <packaging>jar</packaging>

              <properties>
                  <maven.compiler.source>17</maven.compiler.source>
                  <maven.compiler.target>17</maven.compiler.target>
                  <spring.boot.version>3.2.0</spring.boot.version>
              </properties>

              <dependencies>
                  <dependency>
                      <groupId>org.springframework.boot</groupId>
                      <artifactId>spring-boot-starter-web</artifactId>
                      <version>\${spring.boot.version}</version>
                  </dependency>
                  <dependency>
                      <groupId>io.opentelemetry</groupId>
                      <artifactId>opentelemetry-api</artifactId>
                      <version>1.32.0</version>
                  </dependency>
              </dependencies>

              <build>
                  <plugins>
                      <plugin>
                          <groupId>org.springframework.boot</groupId>
                          <artifactId>spring-boot-maven-plugin</artifactId>
                          <version>\${spring.boot.version}</version>
                      </plugin>
                  </plugins>
              </build>
          </project>
          EOF

          cat > src/main/java/com/example/Application.java << 'EOF'
          package com.example;

          import org.springframework.boot.SpringApplication;
          import org.springframework.boot.autoconfigure.SpringBootApplication;
          import org.springframework.web.bind.annotation.GetMapping;
          import org.springframework.web.bind.annotation.RestController;
          import org.springframework.web.client.RestTemplate;
          import java.time.Instant;
          import java.util.HashMap;
          import java.util.Map;

          @SpringBootApplication
          @RestController
          public class Application {

              private final RestTemplate restTemplate = new RestTemplate();

              public static void main(String[] args) {
                  SpringApplication.run(Application.class, args);
              }

              @GetMapping("/health")
              public Map<String, Object> health() {
                  Map<String, Object> response = new HashMap<>();
                  response.put("service", "java");
                  response.put("status", "healthy");
                  response.put("timestamp", Instant.now().toString());
                  return response;
              }

              @GetMapping("/api/data")
              public Map<String, Object> getData() {
                  try {
                      Map<String, Object> goResponse = restTemplate.getForObject("http://localhost:8090/health", Map.class);
                      Map<String, Object> response = new HashMap<>();
                      response.put("service", "java");
                      response.put("data", "Hello from Java service");
                      response.put("go_service", goResponse);
                      response.put("timestamp", Instant.now().toString());
                      return response;
                  } catch (Exception e) {
                      Map<String, Object> error = new HashMap<>();
                      error.put("error", e.getMessage());
                      return error;
                  }
              }
          }
          EOF

          # Install Maven
          wget https://archive.apache.org/dist/maven/maven-3/3.9.5/binaries/apache-maven-3.9.5-bin.tar.gz
          tar xzf apache-maven-3.9.5-bin.tar.gz
          mv apache-maven-3.9.5 /opt/maven
          export PATH=/opt/maven/bin:$PATH

          # Build Java service
          mvn clean package -DskipTests

          # Create Go service
          cd /opt/distributed-services
          mkdir -p go-service
          cd go-service

          cat > go.mod << 'EOF'
          module go-service

          go 1.21

          require (
              github.com/gin-gonic/gin v1.9.1
              go.opentelemetry.io/otel v1.21.0
              go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.21.0
              go.opentelemetry.io/otel/sdk v1.21.0
              go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin v0.46.1
          )
          EOF

          cat > main.go << 'EOF'
          package main

          import (
              "context"
              "encoding/json"
              "fmt"
              "log"
              "net/http"
              "time"

              "github.com/gin-gonic/gin"
              "go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
              "go.opentelemetry.io/otel"
              "go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
              "go.opentelemetry.io/otel/sdk/resource"
              "go.opentelemetry.io/otel/sdk/trace"
              semconv "go.opentelemetry.io/otel/semconv/v1.21.0"
          )

          func initTracer() func() {
              ctx := context.Background()

              exporter, err := otlptracegrpc.New(ctx,
                  otlptracegrpc.WithEndpoint("http://localhost:4317"),
                  otlptracegrpc.WithInsecure(),
              )
              if err != nil {
                  log.Fatal("Failed to create OTLP trace exporter: ", err)
              }

              res, err := resource.New(ctx,
                  resource.WithAttributes(
                      semconv.ServiceName("go-service"),
                      semconv.ServiceVersion("1.0.0"),
                  ),
              )
              if err != nil {
                  log.Fatal("Failed to create resource: ", err)
              }

              tp := trace.NewTracerProvider(
                  trace.WithBatcher(exporter),
                  trace.WithResource(res),
              )

              otel.SetTracerProvider(tp)

              return func() {
                  if err := tp.Shutdown(ctx); err != nil {
                      log.Printf("Error shutting down tracer provider: %v", err)
                  }
              }
          }

          func main() {
              cleanup := initTracer()
              defer cleanup()

              r := gin.Default()
              r.Use(otelgin.Middleware("go-service"))

              r.GET("/health", func(c *gin.Context) {
                  c.JSON(http.StatusOK, gin.H{
                      "service":   "go",
                      "status":    "healthy",
                      "timestamp": time.Now().Format(time.RFC3339),
                  })
              })

              r.GET("/api/data", func(c *gin.Context) {
                  // Call Node.js service
                  resp, err := http.Get("http://localhost:3000/health")
                  if err != nil {
                      c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                      return
                  }
                  defer resp.Body.Close()

                  var nodeResponse map[string]interface{}
                  json.NewDecoder(resp.Body).Decode(&nodeResponse)

                  c.JSON(http.StatusOK, gin.H{
                      "service":      "go",
                      "data":         "Hello from Go service",
                      "node_service": nodeResponse,
                      "timestamp":    time.Now().Format(time.RFC3339),
                  })
              })

              fmt.Println("Go service listening at http://localhost:8090")
              r.Run(":8090")
          }
          EOF

          # Build Go service
          go mod tidy
          go build -o go-service main.go

          # Download and install OpenTelemetry Collector Contrib
          cd /opt/distributed-services
          wget https://github.com/open-telemetry/opentelemetry-collector-releases/releases/latest/download/otelcol-contrib_linux_amd64.tar.gz
          tar -xzf otelcol-contrib_linux_amd64.tar.gz
          mv otelcol-contrib /usr/local/bin/

          # Create OTEL Collector configuration
          cat > otel-collector-config.yaml << EOF
          receivers:
            otlp:
              protocols:
                grpc:
                  endpoint: 0.0.0.0:4317
                http:
                  endpoint: 0.0.0.0:4318
            hostmetrics:
              collection_interval: 30s
              scrapers:
                cpu:
                  metrics:
                    system.cpu.utilization:
                      enabled: true
                memory:
                  metrics:
                    system.memory.utilization:
                      enabled: true
                disk:
                  metrics:
                    system.disk.io.time:
                      enabled: true
                network:
                  metrics:
                    system.network.io:
                      enabled: true
                filesystem:
                  metrics:
                    system.filesystem.utilization:
                      enabled: true
            filelog:
              include:
                - /var/log/messages
                - /var/log/secure
                - /opt/distributed-services/*/logs/*.log
              operators:
                - type: json_parser
                  id: json_parser
                  if: 'body matches "^\\{"'

          processors:
            batch:
              timeout: 1s
              send_batch_size: 1024
            memory_limiter:
              limit_mib: 512
            spanmetrics:
              metrics_exporter: coralogix
              latency_histogram_buckets: [100us, 1ms, 2ms, 6ms, 10ms, 100ms, 250ms]
              dimensions_cache_size: 1000
              aggregation_temporality: "AGGREGATION_TEMPORALITY_CUMULATIVE"

          exporters:
            coralogix:
              domain: "coralogix.ap-southeast-1.coralogix.com"
              private_key: "\${CORALOGIX_PRIVATE_KEY}"
              application_name: "\${CORALOGIX_APPLICATION_NAME}"
              subsystem_name: "\${CORALOGIX_SUBSYSTEM_NAME}"
              timeout: 30s
            logging:
              loglevel: debug

          service:
            pipelines:
              traces:
                receivers: [otlp]
                processors: [memory_limiter, batch, spanmetrics]
                exporters: [coralogix, logging]
              metrics:
                receivers: [otlp, hostmetrics]
                processors: [memory_limiter, batch]
                exporters: [coralogix, logging]
              logs:
                receivers: [otlp, filelog]
                processors: [memory_limiter, batch]
                exporters: [coralogix, logging]
          EOF

          # Create systemd service for OTEL Collector
          cat > /etc/systemd/system/otel-collector.service << EOF
          [Unit]
          Description=OpenTelemetry Collector
          After=network.target

          [Service]
          Type=simple
          User=root
          ExecStart=/usr/local/bin/otelcol-contrib --config=/opt/distributed-services/otel-collector-config.yaml
          Restart=always
          RestartSec=5
          Environment=CORALOGIX_PRIVATE_KEY=${CoralogixPrivateKey}
          Environment=CORALOGIX_APPLICATION_NAME=${CoralogixApplicationName}
          Environment=CORALOGIX_SUBSYSTEM_NAME=${CoralogixSubsystemName}

          [Install]
          WantedBy=multi-user.target
          EOF

          # Install and configure CloudWatch Agent
          wget https://s3.amazonaws.com/amazoncloudwatch-agent/amazon_linux/amd64/latest/amazon-cloudwatch-agent.rpm
          rpm -U ./amazon-cloudwatch-agent.rpm

          # Create CloudWatch Agent configuration
          cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << 'EOF'
          {
            "agent": {
              "metrics_collection_interval": 60,
              "run_as_user": "cwagent"
            },
            "metrics": {
              "namespace": "DistributedServices/EC2",
              "metrics_collected": {
                "cpu": {
                  "measurement": [
                    "cpu_usage_idle",
                    "cpu_usage_iowait",
                    "cpu_usage_user",
                    "cpu_usage_system"
                  ],
                  "metrics_collection_interval": 60,
                  "totalcpu": false
                },
                "disk": {
                  "measurement": [
                    "used_percent"
                  ],
                  "metrics_collection_interval": 60,
                  "resources": [
                    "*"
                  ]
                },
                "diskio": {
                  "measurement": [
                    "io_time"
                  ],
                  "metrics_collection_interval": 60,
                  "resources": [
                    "*"
                  ]
                },
                "mem": {
                  "measurement": [
                    "mem_used_percent"
                  ],
                  "metrics_collection_interval": 60
                },
                "netstat": {
                  "measurement": [
                    "tcp_established",
                    "tcp_time_wait"
                  ],
                  "metrics_collection_interval": 60
                },
                "swap": {
                  "measurement": [
                    "swap_used_percent"
                  ],
                  "metrics_collection_interval": 60
                }
              }
            },
            "logs": {
              "logs_collected": {
                "files": {
                  "collect_list": [
                    {
                      "file_path": "/var/log/messages",
                      "log_group_name": "/aws/ec2/distributed-services/system",
                      "log_stream_name": "{instance_id}/messages"
                    },
                    {
                      "file_path": "/opt/distributed-services/*/logs/*.log",
                      "log_group_name": "/aws/ec2/distributed-services/applications",
                      "log_stream_name": "{instance_id}/app-logs"
                    }
                  ]
                }
              }
            }
          }
          EOF

          # Create systemd services for applications
          cat > /etc/systemd/system/nodejs-service.service << 'EOF'
          [Unit]
          Description=Node.js Service
          After=network.target otel-collector.service
          Requires=otel-collector.service

          [Service]
          Type=simple
          User=ec2-user
          WorkingDirectory=/opt/distributed-services/nodejs-service
          ExecStart=/usr/bin/node app.js
          Restart=always
          RestartSec=5
          Environment=NODE_ENV=production
          EnvironmentFile=/opt/distributed-services/.env

          [Install]
          WantedBy=multi-user.target
          EOF

          cat > /etc/systemd/system/python-service.service << 'EOF'
          [Unit]
          Description=Python Service
          After=network.target otel-collector.service
          Requires=otel-collector.service

          [Service]
          Type=simple
          User=ec2-user
          WorkingDirectory=/opt/distributed-services/python-service
          ExecStart=/usr/bin/python3 app.py
          Restart=always
          RestartSec=5
          EnvironmentFile=/opt/distributed-services/.env

          [Install]
          WantedBy=multi-user.target
          EOF

          cat > /etc/systemd/system/java-service.service << 'EOF'
          [Unit]
          Description=Java Service
          After=network.target otel-collector.service
          Requires=otel-collector.service

          [Service]
          Type=simple
          User=ec2-user
          WorkingDirectory=/opt/distributed-services/java-service
          ExecStart=/usr/bin/java -javaagent:/opt/distributed-services/opentelemetry-javaagent.jar -jar target/java-service-1.0.0.jar
          Restart=always
          RestartSec=5
          Environment=JAVA_HOME=/usr/lib/jvm/java-17-amazon-corretto
          EnvironmentFile=/opt/distributed-services/.env

          [Install]
          WantedBy=multi-user.target
          EOF

          cat > /etc/systemd/system/go-service.service << 'EOF'
          [Unit]
          Description=Go Service
          After=network.target otel-collector.service
          Requires=otel-collector.service

          [Service]
          Type=simple
          User=ec2-user
          WorkingDirectory=/opt/distributed-services/go-service
          ExecStart=/opt/distributed-services/go-service/go-service
          Restart=always
          RestartSec=5
          EnvironmentFile=/opt/distributed-services/.env

          [Install]
          WantedBy=multi-user.target
          EOF

          # Download OpenTelemetry Java agent
          wget -O /opt/distributed-services/opentelemetry-javaagent.jar https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar

          # Set proper permissions
          chown -R ec2-user:ec2-user /opt/distributed-services
          chmod +x /opt/distributed-services/go-service/go-service

          # Create log directories
          mkdir -p /opt/distributed-services/nodejs-service/logs
          mkdir -p /opt/distributed-services/python-service/logs
          mkdir -p /opt/distributed-services/java-service/logs
          mkdir -p /opt/distributed-services/go-service/logs
          chown -R ec2-user:ec2-user /opt/distributed-services/*/logs

          # Enable and start services
          systemctl daemon-reload
          systemctl enable otel-collector
          systemctl enable nodejs-service
          systemctl enable python-service
          systemctl enable java-service
          systemctl enable go-service

          # Start OTEL Collector first
          systemctl start otel-collector
          sleep 10

          # Start application services
          systemctl start nodejs-service
          systemctl start python-service
          systemctl start java-service
          systemctl start go-service

          # Start CloudWatch Agent
          /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s

          # Install and start SSM Agent (should already be installed on Amazon Linux 2023)
          systemctl enable amazon-ssm-agent
          systemctl start amazon-ssm-agent

          # Create a health check script
          cat > /opt/distributed-services/health-check.sh << 'EOF'
          #!/bin/bash
          echo "=== Distributed Services Health Check ==="
          echo "Timestamp: \$(date)"
          echo

          services=("nodejs-service:3000" "python-service:5000" "java-service:8080" "go-service:8090")

          for service in "\${services[@]}"; do
              name=\$(echo \$service | cut -d: -f1)
              port=\$(echo \$service | cut -d: -f2)

              echo "Checking \$name on port \$port..."
              if curl -s -f http://localhost:\$port/health > /dev/null; then
                  echo "[OK] \$name is healthy"
              else
                  echo "[FAIL] \$name is not responding"
              fi
          done

          echo
          echo "=== Service Status ==="
          systemctl status otel-collector --no-pager -l
          systemctl status nodejs-service --no-pager -l
          systemctl status python-service --no-pager -l
          systemctl status java-service --no-pager -l
          systemctl status go-service --no-pager -l
          EOF

          chmod +x /opt/distributed-services/health-check.sh

          # Run initial health check
          sleep 30
          /opt/distributed-services/health-check.sh > /var/log/initial-health-check.log 2>&1

          echo "Distributed services setup completed successfully!" > /var/log/setup-complete.log

Outputs:
  InstanceId:
    Description: EC2 Instance ID
    Value: !Ref EC2Instance
    Export:
      Name: !Sub '${AWS::StackName}_InstanceId'

  InstancePublicIP:
    Description: Public IP address of the EC2 instance
    Value: !GetAtt EC2Instance.PublicIp
    Export:
      Name: !Sub '${AWS::StackName}_PublicIP'

  InstancePrivateIP:
    Description: Private IP address of the EC2 instance
    Value: !GetAtt EC2Instance.PrivateIp
    Export:
      Name: !Sub '${AWS::StackName}_PrivateIP'

  NodeJSServiceURL:
    Description: Node.js service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:3000'
    Export:
      Name: !Sub '${AWS::StackName}_NodeJSURL'

  PythonServiceURL:
    Description: Python service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:5000'
    Export:
      Name: !Sub '${AWS::StackName}_PythonURL'

  JavaServiceURL:
    Description: Java service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:8080'
    Export:
      Name: !Sub '${AWS::StackName}_JavaURL'

  GoServiceURL:
    Description: Go service URL
    Value: !Sub 'http://${EC2Instance.PublicIp}:8090'
    Export:
      Name: !Sub '${AWS::StackName}_GoURL'

  SSMSessionCommand:
    Description: Command to connect via SSM Session Manager
    Value: !Sub 'aws ssm start-session --target ${EC2Instance}'
    Export:
      Name: !Sub '${AWS::StackName}_SSMCommand'