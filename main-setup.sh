#!/bin/bash
set -e

echo "Starting distributed services setup..."

# Create log directory
mkdir -p /opt/distributed-services/logs

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a /opt/distributed-services/logs/setup.log
}

log "Setting up OpenTelemetry Collector..."
bash /opt/distributed-services/setup-otel-collector.sh

log "Setting up Node.js service..."
bash /opt/distributed-services/setup-nodejs-service.sh

log "Setting up Python service..."
bash /opt/distributed-services/setup-python-service.sh

log "Setting up Java service..."
bash /opt/distributed-services/setup-java-service.sh

log "Setting up Go service..."
bash /opt/distributed-services/setup-go-service.sh

log "Setting up CloudWatch Agent..."
bash /opt/distributed-services/setup-cloudwatch-agent.sh

# Set proper permissions
chown -R ec2-user:ec2-user /opt/distributed-services
chmod +x /opt/distributed-services/go-service/go-service

# Create log directories for services
mkdir -p /opt/distributed-services/nodejs-service/logs
mkdir -p /opt/distributed-services/python-service/logs
mkdir -p /opt/distributed-services/java-service/logs
mkdir -p /opt/distributed-services/go-service/logs
chown -R ec2-user:ec2-user /opt/distributed-services/*/logs

# Create health check script
cat > /opt/distributed-services/health-check.sh << 'EOF'
#!/bin/bash
echo "=== Distributed Services Health Check ==="
echo "Timestamp: $(date)"
echo

services=("nodejs-service:3000" "python-service:5000" "java-service:8080" "go-service:8090")

for service in "${services[@]}"; do
    name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    echo "Checking $name on port $port..."
    if curl -s -f http://localhost:$port/health > /dev/null; then
        echo "[OK] $name is healthy"
    else
        echo "[FAIL] $name is not responding"
    fi
done

echo
echo "=== Service Status ==="
systemctl status otel-collector --no-pager -l
systemctl status nodejs-service --no-pager -l
systemctl status python-service --no-pager -l
systemctl status java-service --no-pager -l
systemctl status go-service --no-pager -l
EOF

chmod +x /opt/distributed-services/health-check.sh

# Enable and start services
log "Enabling and starting services..."
systemctl daemon-reload

# Enable services
systemctl enable otel-collector
systemctl enable nodejs-service
systemctl enable python-service
systemctl enable java-service
systemctl enable go-service

# Start OTEL Collector first
log "Starting OpenTelemetry Collector..."
systemctl start otel-collector
sleep 10

# Start application services
log "Starting application services..."
systemctl start nodejs-service
systemctl start python-service
systemctl start java-service
systemctl start go-service

# Start CloudWatch Agent
log "Starting CloudWatch Agent..."
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s

# Enable and start SSM Agent
log "Starting SSM Agent..."
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent

# Wait for services to start
log "Waiting for services to start..."
sleep 30

# Run health check
log "Running initial health check..."
/opt/distributed-services/health-check.sh > /opt/distributed-services/logs/initial-health-check.log 2>&1

log "Distributed services setup completed successfully!"
echo "Setup completed at $(date)" > /var/log/setup-complete.log
