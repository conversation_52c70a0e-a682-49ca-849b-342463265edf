#!/bin/bash
set -e

echo "Setting up Go service..."

# Create Go service directory
mkdir -p /opt/distributed-services/go-service
cd /opt/distributed-services/go-service

# Create go.mod
cat > go.mod << 'EOF'
module go-service

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    go.opentelemetry.io/otel v1.21.0
    go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.21.0
    go.opentelemetry.io/otel/sdk v1.21.0
    go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin v0.46.1
)
EOF

# Create Go application
cat > main.go << 'EOF'
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "time"
    
    "github.com/gin-gonic/gin"
    "go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
    "go.opentelemetry.io/otel/sdk/resource"
    "go.opentelemetry.io/otel/sdk/trace"
    semconv "go.opentelemetry.io/otel/semconv/v1.21.0"
)

func initTracer() func() {
    ctx := context.Background()
    
    exporter, err := otlptracegrpc.New(ctx,
        otlptracegrpc.WithEndpoint("http://localhost:4317"),
        otlptracegrpc.WithInsecure(),
    )
    if err != nil {
        log.Fatal("Failed to create OTLP trace exporter: ", err)
    }
    
    res, err := resource.New(ctx,
        resource.WithAttributes(
            semconv.ServiceName("go-service"),
            semconv.ServiceVersion("1.0.0"),
        ),
    )
    if err != nil {
        log.Fatal("Failed to create resource: ", err)
    }
    
    tp := trace.NewTracerProvider(
        trace.WithBatcher(exporter),
        trace.WithResource(res),
    )
    
    otel.SetTracerProvider(tp)
    
    return func() {
        if err := tp.Shutdown(ctx); err != nil {
            log.Printf("Error shutting down tracer provider: %v", err)
        }
    }
}

func main() {
    cleanup := initTracer()
    defer cleanup()
    
    r := gin.Default()
    r.Use(otelgin.Middleware("go-service"))
    
    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "service":   "go",
            "status":    "healthy",
            "timestamp": time.Now().Format(time.RFC3339),
        })
    })
    
    r.GET("/api/data", func(c *gin.Context) {
        // Call Node.js service
        resp, err := http.Get("http://localhost:3000/health")
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
        defer resp.Body.Close()
        
        var nodeResponse map[string]interface{}
        json.NewDecoder(resp.Body).Decode(&nodeResponse)
        
        c.JSON(http.StatusOK, gin.H{
            "service":      "go",
            "data":         "Hello from Go service",
            "node_service": nodeResponse,
            "timestamp":    time.Now().Format(time.RFC3339),
        })
    })
    
    fmt.Println("Go service listening at http://localhost:8090")
    r.Run(":8090")
}
EOF

# Build Go service
go mod tidy
go build -o go-service main.go

# Create systemd service
cat > /etc/systemd/system/go-service.service << 'EOF'
[Unit]
Description=Go Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/distributed-services/go-service
ExecStart=/opt/distributed-services/go-service/go-service
Restart=always
RestartSec=5
EnvironmentFile=/opt/distributed-services/.env

[Install]
WantedBy=multi-user.target
EOF

echo "Go service setup completed"
