#!/bin/bash
set -e

echo "Setting up Java service..."

# Create Java service directory
mkdir -p /opt/distributed-services/java-service/src/main/java/com/example
cd /opt/distributed-services/java-service

# Create pom.xml
cat > pom.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>java-service</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring.boot.version>3.2.0</spring.boot.version>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-api</artifactId>
            <version>1.32.0</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
            </plugin>
        </plugins>
    </build>
</project>
EOF

# Create Java application
cat > src/main/java/com/example/Application.java << 'EOF'
package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

@SpringBootApplication
@RestController
public class Application {

    private final RestTemplate restTemplate = new RestTemplate();
    private final Random random = new Random();
    private final AtomicInteger requestCounter = new AtomicInteger(0);

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "java");
        response.put("status", "healthy");
        response.put("timestamp", Instant.now().toString());
        response.put("requests_processed", requestCounter.get());
        return response;
    }

    @GetMapping("/api/data")
    public ResponseEntity<Map<String, Object>> getData(@RequestParam(defaultValue = "false") String simulate_error) {
        requestCounter.incrementAndGet();

        // Simulate various response scenarios
        if ("true".equals(simulate_error) || random.nextInt(100) < 15) { // 15% chance of error
            return simulateError();
        }

        try {
            Map<String, Object> goResponse = restTemplate.getForObject("http://localhost:8090/health", Map.class);
            Map<String, Object> response = new HashMap<>();
            response.put("service", "java");
            response.put("data", "Hello from Java service");
            response.put("go_service", goResponse);
            response.put("timestamp", Instant.now().toString());
            response.put("request_id", "java-" + requestCounter.get());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("service", "java");
            error.put("error", "Failed to call Go service: " + e.getMessage());
            error.put("timestamp", Instant.now().toString());
            error.put("request_id", "java-" + requestCounter.get());
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(error);
        }
    }

    @GetMapping("/api/users/{userId}")
    public ResponseEntity<Map<String, Object>> getUser(@PathVariable String userId) {
        requestCounter.incrementAndGet();

        Map<String, Object> response = new HashMap<>();
        response.put("service", "java");
        response.put("timestamp", Instant.now().toString());
        response.put("request_id", "java-" + requestCounter.get());

        // Simulate user validation
        if (userId == null || userId.trim().isEmpty()) {
            response.put("error", "User ID is required");
            return ResponseEntity.badRequest().body(response);
        }

        if ("invalid".equals(userId)) {
            response.put("error", "Invalid user ID format");
            return ResponseEntity.badRequest().body(response);
        }

        if ("notfound".equals(userId)) {
            response.put("error", "User not found");
            return ResponseEntity.notFound().build();
        }

        if ("forbidden".equals(userId)) {
            response.put("error", "Access forbidden for this user");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
        }

        // Simulate successful user data
        response.put("user_id", userId);
        response.put("username", "user_" + userId);
        response.put("email", "user" + userId + "@example.com");
        response.put("status", "active");

        return ResponseEntity.ok(response);
    }

    @GetMapping("/api/slow")
    public ResponseEntity<Map<String, Object>> slowEndpoint() {
        requestCounter.incrementAndGet();

        try {
            // Simulate slow processing
            Thread.sleep(2000 + random.nextInt(3000)); // 2-5 seconds

            Map<String, Object> response = new HashMap<>();
            response.put("service", "java");
            response.put("data", "Slow operation completed");
            response.put("processing_time_ms", 2000 + random.nextInt(3000));
            response.put("timestamp", Instant.now().toString());
            response.put("request_id", "java-" + requestCounter.get());

            return ResponseEntity.ok(response);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            Map<String, Object> error = new HashMap<>();
            error.put("service", "java");
            error.put("error", "Operation interrupted");
            error.put("timestamp", Instant.now().toString());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    private ResponseEntity<Map<String, Object>> simulateError() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "java");
        response.put("timestamp", Instant.now().toString());
        response.put("request_id", "java-" + requestCounter.get());

        int errorType = random.nextInt(4);

        switch (errorType) {
            case 0: // 400 Bad Request
                response.put("error", "Invalid request parameters");
                return ResponseEntity.badRequest().body(response);
            case 1: // 401 Unauthorized
                response.put("error", "Authentication required");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            case 2: // 500 Internal Server Error
                response.put("error", "Internal server error occurred");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            default: // 503 Service Unavailable
                response.put("error", "Service temporarily unavailable");
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
        }
    }
}
EOF

# Install Maven
wget https://archive.apache.org/dist/maven/maven-3/3.9.5/binaries/apache-maven-3.9.5-bin.tar.gz
tar xzf apache-maven-3.9.5-bin.tar.gz
mv apache-maven-3.9.5 /opt/maven
export PATH=/opt/maven/bin:$PATH

# Build Java service
mvn clean package -DskipTests

# Download OpenTelemetry Java agent
wget -O /opt/distributed-services/opentelemetry-javaagent.jar https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar

# Create systemd service
cat > /etc/systemd/system/java-service.service << 'EOF'
[Unit]
Description=Java Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/distributed-services/java-service
ExecStart=/usr/bin/java -javaagent:/opt/distributed-services/opentelemetry-javaagent.jar -jar target/java-service-1.0.0.jar
Restart=always
RestartSec=5
Environment=JAVA_HOME=/usr/lib/jvm/java-17-amazon-corretto
EnvironmentFile=/opt/distributed-services/.env

[Install]
WantedBy=multi-user.target
EOF

echo "Java service setup completed"
