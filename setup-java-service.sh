#!/bin/bash
set -e

echo "Setting up Java service..."

# Create Java service directory
mkdir -p /opt/distributed-services/java-service/src/main/java/com/example
cd /opt/distributed-services/java-service

# Create pom.xml
cat > pom.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>java-service</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring.boot.version>3.2.0</spring.boot.version>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-api</artifactId>
            <version>1.32.0</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
            </plugin>
        </plugins>
    </build>
</project>
EOF

# Create Java application
cat > src/main/java/com/example/Application.java << 'EOF'
package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@SpringBootApplication
@RestController
public class Application {
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
    
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "java");
        response.put("status", "healthy");
        response.put("timestamp", Instant.now().toString());
        return response;
    }
    
    @GetMapping("/api/data")
    public Map<String, Object> getData() {
        try {
            Map<String, Object> goResponse = restTemplate.getForObject("http://localhost:8090/health", Map.class);
            Map<String, Object> response = new HashMap<>();
            response.put("service", "java");
            response.put("data", "Hello from Java service");
            response.put("go_service", goResponse);
            response.put("timestamp", Instant.now().toString());
            return response;
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            return error;
        }
    }
}
EOF

# Install Maven
wget https://archive.apache.org/dist/maven/maven-3/3.9.5/binaries/apache-maven-3.9.5-bin.tar.gz
tar xzf apache-maven-3.9.5-bin.tar.gz
mv apache-maven-3.9.5 /opt/maven
export PATH=/opt/maven/bin:$PATH

# Build Java service
mvn clean package -DskipTests

# Download OpenTelemetry Java agent
wget -O /opt/distributed-services/opentelemetry-javaagent.jar https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar

# Create systemd service
cat > /etc/systemd/system/java-service.service << 'EOF'
[Unit]
Description=Java Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/distributed-services/java-service
ExecStart=/usr/bin/java -javaagent:/opt/distributed-services/opentelemetry-javaagent.jar -jar target/java-service-1.0.0.jar
Restart=always
RestartSec=5
Environment=JAVA_HOME=/usr/lib/jvm/java-17-amazon-corretto
EnvironmentFile=/opt/distributed-services/.env

[Install]
WantedBy=multi-user.target
EOF

echo "Java service setup completed"
