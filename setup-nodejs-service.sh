#!/bin/bash
set -e

echo "Setting up Node.js service..."

# Create Node.js service directory
mkdir -p /opt/distributed-services/nodejs-service
cd /opt/distributed-services/nodejs-service

# Create package.json
cat > package.json << 'EOF'
{
  "name": "nodejs-service",
  "version": "1.0.0",
  "description": "Node.js service with OpenTelemetry",
  "main": "app.js",
  "scripts": {
    "start": "node app.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "@opentelemetry/api": "^1.7.0",
    "@opentelemetry/sdk-node": "^0.45.0",
    "@opentelemetry/auto-instrumentations-node": "^0.40.0",
    "@opentelemetry/exporter-otlp-grpc": "^0.45.0",
    "axios": "^1.6.0"
  }
}
EOF

# Create Node.js application
cat > app.js << 'EOF'
const { NodeSDK } = require('@opentelemetry/sdk-node');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { OTLPTraceExporter } = require('@opentelemetry/exporter-otlp-grpc');

const sdk = new NodeSDK({
  traceExporter: new OTLPTraceExporter({
    url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4317',
  }),
  instrumentations: [getNodeAutoInstrumentations()],
});

sdk.start();

const express = require('express');
const axios = require('axios');
const app = express();
const port = 3000;

app.use(express.json());

app.get('/health', (req, res) => {
  res.json({ 
    service: 'nodejs', 
    status: 'healthy', 
    timestamp: new Date().toISOString() 
  });
});

app.get('/api/data', async (req, res) => {
  try {
    // Call Python service
    const pythonResponse = await axios.get('http://localhost:5000/health');
    res.json({
      service: 'nodejs',
      data: 'Hello from Node.js service',
      python_service: pythonResponse.data,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(port, () => {
  console.log('Node.js service listening at http://localhost:' + port);
});
EOF

# Install dependencies
npm install

# Create systemd service
cat > /etc/systemd/system/nodejs-service.service << 'EOF'
[Unit]
Description=Node.js Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/distributed-services/nodejs-service
ExecStart=/usr/bin/node app.js
Restart=always
RestartSec=5
Environment=NODE_ENV=production
EnvironmentFile=/opt/distributed-services/.env

[Install]
WantedBy=multi-user.target
EOF

echo "Node.js service setup completed"
