#!/bin/bash
set -e

echo "Setting up Python service..."

# Create Python service directory
mkdir -p /opt/distributed-services/python-service
cd /opt/distributed-services/python-service

# Create requirements.txt
cat > requirements.txt << 'EOF'
flask==3.0.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-flask==0.42b0
opentelemetry-instrumentation-requests==0.42b0
opentelemetry-exporter-otlp-proto-grpc==1.21.0
requests==2.31.0
EOF

# Create Python application
cat > app.py << 'EOF'
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.flask import FlaskInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from flask import Flask, jsonify, request, abort
import requests
import os
import random
import time
from datetime import datetime
from threading import Lock

# Initialize OpenTelemetry
trace.set_tracer_provider(TracerProvider())
tracer = trace.get_tracer(__name__)

otlp_exporter = OTLPSpanExporter(
    endpoint=os.getenv('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317'),
    insecure=True
)

span_processor = BatchSpanProcessor(otlp_exporter)
trace.get_tracer_provider().add_span_processor(span_processor)

app = Flask(__name__)

# Instrument Flask and requests
FlaskInstrumentor().instrument_app(app)
RequestsInstrumentor().instrument()

# Global request counter
request_counter = 0
counter_lock = Lock()

def increment_counter():
    global request_counter
    with counter_lock:
        request_counter += 1
        return request_counter

@app.before_request
def before_request():
    # Simulate random delays for some requests
    if random.random() < 0.08:  # 8% chance of delay
        time.sleep(random.uniform(0.5, 2.0))

@app.route('/health')
def health():
    return jsonify({
        'service': 'python',
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'requests_processed': request_counter
    })

@app.route('/api/data')
def get_data():
    req_id = f"python-{increment_counter()}"

    # Simulate various response scenarios
    simulate_error = request.args.get('simulate_error') == 'true' or random.random() < 0.13  # 13% chance of error

    if simulate_error:
        return simulate_error_response(req_id)

    try:
        # Call Java service
        java_response = requests.get('http://localhost:8080/health', timeout=5)
        return jsonify({
            'service': 'python',
            'data': 'Hello from Python service',
            'java_service': java_response.json(),
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        })
    except requests.exceptions.Timeout:
        return jsonify({
            'service': 'python',
            'error': 'Java service timeout',
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        }), 504
    except Exception as e:
        return jsonify({
            'service': 'python',
            'error': f'Failed to call Java service: {str(e)}',
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        }), 503

@app.route('/api/products/<product_id>')
def get_product(product_id):
    req_id = f"python-{increment_counter()}"

    # Simulate product validation
    if not product_id or product_id.strip() == '':
        return jsonify({
            'service': 'python',
            'error': 'Product ID is required',
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        }), 400

    if product_id == 'invalid':
        return jsonify({
            'service': 'python',
            'error': 'Invalid product ID format',
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        }), 400

    if product_id == 'notfound':
        return jsonify({
            'service': 'python',
            'error': 'Product not found',
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        }), 404

    if product_id == 'discontinued':
        return jsonify({
            'service': 'python',
            'error': 'Product has been discontinued',
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        }), 410  # Gone

    # Simulate successful product data
    return jsonify({
        'service': 'python',
        'product_id': product_id,
        'name': f'Product {product_id}',
        'price': round(random.uniform(10.0, 999.99), 2),
        'in_stock': random.choice([True, False]),
        'category': random.choice(['electronics', 'clothing', 'books', 'home']),
        'timestamp': datetime.now().isoformat(),
        'request_id': req_id
    })

@app.route('/api/calculate', methods=['POST'])
def calculate():
    req_id = f"python-{increment_counter()}"

    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'service': 'python',
                'error': 'JSON data required',
                'timestamp': datetime.now().isoformat(),
                'request_id': req_id
            }), 400

        operation = data.get('operation')
        a = data.get('a')
        b = data.get('b')

        if not all([operation, a is not None, b is not None]):
            return jsonify({
                'service': 'python',
                'error': 'Missing required fields: operation, a, b',
                'timestamp': datetime.now().isoformat(),
                'request_id': req_id
            }), 422

        # Simulate calculation with potential errors
        if operation == 'divide' and b == 0:
            return jsonify({
                'service': 'python',
                'error': 'Division by zero not allowed',
                'timestamp': datetime.now().isoformat(),
                'request_id': req_id
            }), 400

        if operation not in ['add', 'subtract', 'multiply', 'divide']:
            return jsonify({
                'service': 'python',
                'error': 'Unsupported operation',
                'timestamp': datetime.now().isoformat(),
                'request_id': req_id
            }), 400

        # Perform calculation
        if operation == 'add':
            result = a + b
        elif operation == 'subtract':
            result = a - b
        elif operation == 'multiply':
            result = a * b
        elif operation == 'divide':
            result = a / b

        return jsonify({
            'service': 'python',
            'operation': operation,
            'a': a,
            'b': b,
            'result': result,
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        })

    except Exception as e:
        return jsonify({
            'service': 'python',
            'error': f'Calculation error: {str(e)}',
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        }), 500

@app.route('/api/heavy')
def heavy_operation():
    req_id = f"python-{increment_counter()}"

    try:
        # Simulate heavy computation
        processing_time = random.uniform(3.0, 8.0)
        time.sleep(processing_time)

        # Random chance of failure during heavy operation
        if random.random() < 0.2:  # 20% chance of failure
            return jsonify({
                'service': 'python',
                'error': 'Heavy operation failed due to resource exhaustion',
                'timestamp': datetime.now().isoformat(),
                'request_id': req_id
            }), 500

        return jsonify({
            'service': 'python',
            'data': 'Heavy operation completed successfully',
            'processing_time_seconds': round(processing_time, 2),
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        })

    except Exception as e:
        return jsonify({
            'service': 'python',
            'error': f'Heavy operation error: {str(e)}',
            'timestamp': datetime.now().isoformat(),
            'request_id': req_id
        }), 500

def simulate_error_response(req_id):
    error_scenarios = [
        (400, 'Bad request: Invalid query parameters'),
        (401, 'Unauthorized: Missing authentication token'),
        (403, 'Forbidden: Access denied to resource'),
        (408, 'Request timeout: Operation took too long'),
        (429, 'Too many requests: Rate limit exceeded'),
        (500, 'Internal server error: Database connection lost'),
        (502, 'Bad gateway: Upstream service returned invalid response'),
        (503, 'Service unavailable: System overloaded')
    ]

    status_code, error_message = random.choice(error_scenarios)

    return jsonify({
        'service': 'python',
        'error': error_message,
        'timestamp': datetime.now().isoformat(),
        'request_id': req_id
    }), status_code

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
EOF

# Install dependencies
pip3 install -r requirements.txt

# Create systemd service
cat > /etc/systemd/system/python-service.service << 'EOF'
[Unit]
Description=Python Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/distributed-services/python-service
ExecStart=/usr/bin/python3 app.py
Restart=always
RestartSec=5
EnvironmentFile=/opt/distributed-services/.env

[Install]
WantedBy=multi-user.target
EOF

echo "Python service setup completed"
