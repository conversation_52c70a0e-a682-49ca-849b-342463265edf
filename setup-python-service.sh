#!/bin/bash
set -e

echo "Setting up Python service..."

# Create Python service directory
mkdir -p /opt/distributed-services/python-service
cd /opt/distributed-services/python-service

# Create requirements.txt
cat > requirements.txt << 'EOF'
flask==3.0.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-flask==0.42b0
opentelemetry-instrumentation-requests==0.42b0
opentelemetry-exporter-otlp-proto-grpc==1.21.0
requests==2.31.0
EOF

# Create Python application
cat > app.py << 'EOF'
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.flask import FlaskInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from flask import Flask, jsonify
import requests
import os
from datetime import datetime

# Initialize OpenTelemetry
trace.set_tracer_provider(TracerProvider())
tracer = trace.get_tracer(__name__)

otlp_exporter = OTLPSpanExporter(
    endpoint=os.getenv('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317'),
    insecure=True
)

span_processor = BatchSpanProcessor(otlp_exporter)
trace.get_tracer_provider().add_span_processor(span_processor)

app = Flask(__name__)

# Instrument Flask and requests
FlaskInstrumentor().instrument_app(app)
RequestsInstrumentor().instrument()

@app.route('/health')
def health():
    return jsonify({
        'service': 'python',
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/data')
def get_data():
    try:
        # Call Java service
        java_response = requests.get('http://localhost:8080/health', timeout=5)
        return jsonify({
            'service': 'python',
            'data': 'Hello from Python service',
            'java_service': java_response.json(),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
EOF

# Install dependencies
pip3 install -r requirements.txt

# Create systemd service
cat > /etc/systemd/system/python-service.service << 'EOF'
[Unit]
Description=Python Service
After=network.target otel-collector.service
Requires=otel-collector.service

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/distributed-services/python-service
ExecStart=/usr/bin/python3 app.py
Restart=always
RestartSec=5
EnvironmentFile=/opt/distributed-services/.env

[Install]
WantedBy=multi-user.target
EOF

echo "Python service setup completed"
